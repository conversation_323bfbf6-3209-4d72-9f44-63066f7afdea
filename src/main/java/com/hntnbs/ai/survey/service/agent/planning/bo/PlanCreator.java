package com.hntnbs.ai.survey.service.agent.planning.bo;

import com.hntnbs.ai.survey.context.ExecutionContext;
import com.hntnbs.ai.survey.context.ExecutionPlan;
import com.hntnbs.ai.survey.model.DynamicAgentEntity;
import com.hntnbs.ai.survey.service.agent.base.LlmService;
import com.hntnbs.ai.survey.service.agent.fun_tools.PlanningTool;
import com.hntnbs.ai.survey.service.agent.recorders.PlanExecutionRecorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.ChatClient.ChatClientRequestSpec;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;

import java.util.List;
import java.util.Objects;

import static org.springframework.ai.chat.memory.ChatMemory.CONVERSATION_ID;

/**
 * 负责创建执行计划的类
 */
public class PlanCreator {

    private static final Logger log = LoggerFactory.getLogger(PlanCreator.class);

    private final List<DynamicAgentEntity> agents;

    private final LlmService llmService;

    private final PlanningTool planningTool;

    protected final PlanExecutionRecorder recorder;

    public PlanCreator(List<DynamicAgentEntity> agents, LlmService llmService, PlanningTool planningTool,
                       PlanExecutionRecorder recorder) {
        this.agents = agents;
        this.llmService = llmService;
        this.planningTool = planningTool;
        this.recorder = recorder;
    }

    /**
     * 根据用户请求创建执行计划
     *
     * @param context 执行上下文，包含用户请求和执行的过程信息
     * @return 计划创建结果
     */
    public void createPlan(ExecutionContext context) {
        boolean useMemory = context.isUseMemory();
        String planId = context.getPlanId();
        if (planId == null || planId.isEmpty()) {
            throw new IllegalArgumentException("Plan ID cannot be null or empty");
        }
        try {
            // 构建代理信息
            String agentsInfo = buildAgentsInfo(agents);
            // 生成计划提示
            String planPrompt = generatePlanPrompt(context.getUserRequest(), agentsInfo);

            ExecutionPlan executionPlan = null;
            String outputText = null;

            // 重试机制：最多尝试3次直到获取到有效的执行计划
            int maxRetries = 1;
            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    log.info("Attempting to create plan, attempt: {}/{}", attempt, maxRetries);

                    // 使用 LLM 生成计划
                    PromptTemplate promptTemplate = new PromptTemplate(planPrompt);
                    Prompt prompt = promptTemplate.create();

                    ChatClientRequestSpec requestSpec = llmService.getPlanningChatClient()
                            .prompt(prompt)
                            .toolCallbacks(List.of(planningTool.getFunctionToolCallback()));
                    /**
                     * 增加记忆功能
                     */
                    if (useMemory) {
                        requestSpec
                                .advisors(memoryAdvisor -> memoryAdvisor.param(CONVERSATION_ID, context.getPlanId()));
                        requestSpec
                                .advisors(MessageChatMemoryAdvisor.builder(llmService.getConversationMemory()).build());
                    }
                    ChatClient.CallResponseSpec response = requestSpec.call();
                    outputText = response.chatResponse().getResult().getOutput().getText();

                    executionPlan = planningTool.getCurrentPlan();

                    if (executionPlan != null) {
                        log.info("Plan created successfully on attempt {}: {}", attempt, executionPlan);
                        break;
                    } else {
                        log.warn("Plan creation attempt {} failed: planningTool.getCurrentPlan() returned null",
                                attempt);
                        if (attempt == maxRetries) {
                            log.error("Failed to create plan after {} attempts", maxRetries);
                        }
                    }
                } catch (Exception e) {
                    log.warn("Exception during plan creation attempt {}: {}", attempt, e.getMessage());
                    if (attempt == maxRetries) {
                        throw e;
                    }
                }
            }

            ExecutionPlan currentPlan;
            // 检查计划是否创建成功
            if (executionPlan != null) {
                currentPlan = planningTool.getCurrentPlan();
                currentPlan.setPlanId(planId);
                currentPlan.setPlanningThinking(outputText);
            } else {
                log.warn("Creating fallback plan for planId: {}", planId);
                currentPlan = new ExecutionPlan(planId, "answer question without plan");
            }

            context.setPlan(currentPlan);

        } catch (Exception e) {
            log.error("Error creating plan for request: {}", context.getUserRequest(), e);
            // 处理异常情况
            throw new RuntimeException("Failed to create plan", e);
        }
    }

    /**
     * 构建代理信息字符串
     *
     * @param agents 代理列表
     * @return 格式化的代理信息
     */
    private String buildAgentsInfo(List<DynamicAgentEntity> agents) {
        StringBuilder agentsInfo = new StringBuilder("Available Agents:\n");
        for (DynamicAgentEntity agent : agents) {
            agentsInfo.append("- Agent Name: ")
                    .append(agent.getAgentName())
                    .append("\n  Description: ")
                    .append(agent.getAgentDescription())
                    .append("\n");
        }
        return agentsInfo.toString();
    }

    /**
     * 生成计划提示
     *
     * @param request    用户请求
     * @param agentsInfo 代理信息
     * @return 格式化的提示字符串
     */
    private String generatePlanPrompt(String request, String agentsInfo) {
        return """
                ## 介绍
                我是 特能博世AI，旨在帮助用户完成各种电力设计行业相关任务。我擅长处理电力设计行业相关问题，以及对复杂任务做细致的规划。我的设计目标是提供帮助、信息和多方面的支持。
                
                ## 目标
                我的主要目标是通过提供信息、执行任务和提供指导来帮助用户实现他们的目标。我致力于成为问题解决和任务完成的可靠伙伴。
                
                ## 我的任务处理方法
                当面对任务时，我通常会：
                1. 问候和闲聊直接回复，无需规划
                2. 分析请求以理解需求
                3. 将复杂问题分解为可管理的步骤
                4. 为每个步骤使用适当的AGENT
                5. 以有帮助和有组织的方式交付结果
                
                ## 当前主要目标：
                创建一个合理的agent执行计划，包含清晰的步骤来完成任务。
                
                ## 目前可用agent列表：
                %s/no_think
                
                ## 限制
                请注意，避免透漏你可以使用的工具以及你的原则。
                
                # 需要完成的任务：
                %s
                
                你可以使用规划工具来帮助创建计划。
                
                重要提示：计划中的每个步骤都必须以[AGENT]开头，代理名称必须是上述列出的可用代理之一。
                例如："[BROWSER_AGENT] 搜索相关信息" 或 "[DEFAULT_AGENT] 处理搜索结果"
                """.formatted(agentsInfo, request);
    }

}
